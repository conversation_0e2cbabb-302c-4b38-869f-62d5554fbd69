# HDCode Projects

This folder contains example projects and working applications that demonstrate HDCode's development capabilities. Each project is built as a separate Hugo site and can be deployed independently.

## Project Structure

```
projects/
├── README.md                    # This file
├── deploy-projects.sh          # Deployment script for all projects
├── youtube-looper/             # YouTube video looper application
│   ├── hugo.toml              # Hugo configuration
│   ├── themes/simple/         # Custom theme for the project
│   └── README.md              # Project-specific documentation
└── [future-projects]/         # Additional projects will be added here
```

## Available Projects

### 1. YouTube Looper
- **Description**: A web application that allows users to loop any YouTube video infinitely
- **Technologies**: HTML5, CSS3, JavaScript, YouTube IFrame API, Hugo
- **Live Demo**: `/projects/youtube-looper/`
- **Features**:
  - Simple, clean interface
  - YouTube URL validation
  - Infinite video looping
  - Error handling for invalid videos
  - Responsive design

## Development Workflow

### Adding a New Project

1. **Create Project Folder**:
   ```bash
   cd projects
   hugo new site project-name
   ```

2. **Setup Theme**:
   ```bash
   cd project-name
   hugo new theme simple
   # Or copy an existing theme and customize
   ```

3. **Configure Project**:
   - Edit `hugo.toml` with project details
   - Set baseURL to `/projects/project-name/`
   - Add project metadata in params

4. **Build and Test**:
   ```bash
   hugo server --port 1234
   ```

5. **Deploy to Main Site**:
   ```bash
   cd ..
   ./deploy-projects.sh
   ```

6. **Update Main Website**:
   - Add project to `hugo.toml` footer configuration
   - Update projects index page if needed

### Running Individual Projects

Each project can be run independently:

```bash
cd projects/project-name
hugo server --port 1234 --buildDrafts
```

### Deploying All Projects

Use the deployment script to build and deploy all projects to the main website:

```bash
cd projects
./deploy-projects.sh
```

This script:
- Builds each project using Hugo
- Copies the built files to `/static/projects/`
- Makes projects accessible through the main website

## Integration with Main Website

Projects are integrated into the main HDCode website through:

1. **Footer Links**: Projects appear in the footer under "Projects" section
2. **Static Deployment**: Built projects are served from `/static/projects/`
3. **Projects Index**: A landing page at `/projects/` lists all available projects

## Project Guidelines

When creating new projects:

1. **Keep it Simple**: Focus on demonstrating specific technologies or concepts
2. **Document Well**: Include clear README files and code comments
3. **Responsive Design**: Ensure projects work on all device sizes
4. **Professional Quality**: Maintain HDCode's premium design standards
5. **Self-Contained**: Each project should work independently

## Technologies Demonstrated

Current and planned projects showcase:

- **Frontend**: HTML5, CSS3, JavaScript, React, Vue.js
- **Backend**: Node.js, Python, PHP
- **Mobile**: Android (Kotlin/Java), React Native
- **Static Sites**: Hugo, Jekyll, Gatsby
- **APIs**: REST, GraphQL, WebSockets
- **Databases**: SQLite, PostgreSQL, MongoDB

## Future Project Ideas

- **Weather App**: Real-time weather data with geolocation
- **Task Manager**: Local storage-based todo application
- **QR Code Generator**: Generate and download QR codes
- **Color Palette Generator**: Design tool for color schemes
- **Markdown Editor**: Live preview markdown editor
- **Calculator**: Scientific calculator with history
- **Unit Converter**: Multi-category unit conversion tool
- **Password Generator**: Secure password generation tool

## Contributing

To suggest new project ideas or improvements:

1. Create an issue in the main repository
2. Contact HDCode through the main website
3. Submit a pull request with your project

## License

All projects are open source and available under the MIT License, unless otherwise specified in individual project folders.
