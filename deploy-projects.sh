#!/bin/bash

# HDCode Projects Deployment Script
# This script builds all projects and deploys them to the main website's static folder

echo "🚀 Starting HDCode Projects Deployment..."

# Create projects directory in static folder if it doesn't exist
mkdir -p ../hdcode.dev-private/static/projects

# Deploy YouTube Looper
echo "📹 Building YouTube Looper..."
cd youtube-looper

# Build the Hugo site
hugo --minify --destination ../../hdcode.dev-private/static/projects/youtube-looper

if [ $? -eq 0 ]; then
    echo "✅ YouTube Looper deployed successfully to /static/projects/youtube-looper"

    # Copy config file if it exists (for API key)
    if [ -f "config.json" ]; then
        cp config.json ../../hdcode.dev-private/static/projects/youtube-looper/config.json
        echo "🔑 API config file copied"
    fi
else
    echo "❌ Failed to build YouTube Looper"
    exit 1
fi

cd ..

echo "🎉 All projects deployed successfully!"
echo ""
echo "Available projects:"
echo "  - YouTube Looper: /projects/youtube-looper/"
echo ""
echo "Projects index: /projects/"
