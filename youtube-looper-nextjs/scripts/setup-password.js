#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up password protection in Firebase
 * Usage: node scripts/setup-password.js "your_password_here"
 */

const { initializeApp, getApps } = require('firebase/app');
const { getFirestore, doc, setDoc } = require('firebase/firestore');
const readline = require('readline');

// Get Firebase config from environment variables
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

async function setupPassword() {
  try {
    // Check if Firebase config is available
    if (!firebaseConfig.projectId || !firebaseConfig.apiKey) {
      console.error('❌ Firebase configuration not found.');
      console.error('Make sure your .env.local file is set up with Firebase credentials.');
      process.exit(1);
    }

    // Get password from command line argument or prompt
    let password = process.argv[2];
    
    if (!password) {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      password = await new Promise((resolve) => {
        rl.question('Enter the password for app access: ', (answer) => {
          rl.close();
          resolve(answer);
        });
      });
    }

    if (!password || password.trim() === '') {
      console.error('❌ Password cannot be empty.');
      process.exit(1);
    }

    // Initialize Firebase
    let app;
    if (getApps().length === 0) {
      app = initializeApp(firebaseConfig);
    } else {
      app = getApps()[0];
    }

    const db = getFirestore(app);

    // Set the password in Firestore
    const configRef = doc(db, 'app_config', 'access_control');
    await setDoc(configRef, {
      password: password.trim(),
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    });

    console.log('✅ Password protection has been set up successfully!');
    console.log('🔒 Your app is now password protected.');
    console.log('📝 Users will need to enter this password to access the app.');
    console.log('');
    console.log('To update the password, run this script again with a new password.');
    
  } catch (error) {
    console.error('❌ Error setting up password:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupPassword();
