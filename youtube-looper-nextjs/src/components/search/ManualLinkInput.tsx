'use client'

import { useState, useRef } from 'react'
import { youtubeService } from '@/lib/services/youtube'
import { useDraftQueue } from '@/hooks/useDraftQueue'

interface ManualLinkInputProps {
  placeholder?: string
}

export function ManualLinkInput({ placeholder = 'Paste YouTube link here...' }: ManualLinkInputProps) {
  const [url, setUrl] = useState('')
  const [isAddingVideo, setIsAddingVideo] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const { addToDraft, isCreationMode, isEditMode } = useDraftQueue()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!url.trim() || isAddingVideo) return

    setError(null)
    setSuccess(null)

    // Validate YouTube URL
    if (!youtubeService.isValidYouTubeUrl(url.trim())) {
      setError('Please enter a valid YouTube URL')
      return
    }

    if (!isCreationMode && !isEditMode) {
      setError('Not in creation or edit mode')
      return
    }

    setIsAddingVideo(true)

    const videoId = youtubeService.extractVideoId(url.trim())
    if (!videoId) {
      setError('Could not extract video ID from URL')
      setIsAddingVideo(false)
      return
    }

    console.log('🔗 Adding YouTube video from manual input (with API):', videoId)

    // Get full metadata from YouTube API
    const videoMetadata = await youtubeService.getVideoMetadata(videoId)
    if (!videoMetadata) {
      setError('Could not fetch video metadata from YouTube API')
      setIsAddingVideo(false)
      return
    }
    console.log('✅ Fetched metadata from YouTube API')

    if (videoMetadata) {
      const draftId = addToDraft(videoMetadata)
      if (draftId) {
        console.log('✅ Added video from manual input to draft queue:', videoMetadata.title, 'Draft ID:', draftId)
        setSuccess(`Added "${videoMetadata.title}" to queue`)
        setUrl('') // Clear the input after successful addition

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError('Failed to add video to queue')
      }
    } else {
      setError('Could not create video metadata')
    }

    setIsAddingVideo(false)
  }

  const handleClear = () => {
    setUrl('')
    setError(null)
    setSuccess(null)
    inputRef.current?.focus()
  }

  const isValidUrl = url.trim() && youtubeService.isValidYouTubeUrl(url.trim())

  return (
    <div className="space-y-3">
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder={placeholder}
            disabled={isAddingVideo}
            className="input-field pr-20 pl-12"
          />
          
          {/* Link Icon */}
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-dark-400">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>
            </svg>
          </div>

          {/* Clear Button */}
          {url && (
            <button
              type="button"
              onClick={handleClear}
              className="absolute right-16 top-1/2 transform -translate-y-1/2 p-1 rounded-full text-dark-400 hover:text-white hover:bg-white/10 transition-colors duration-200"
              title="Clear URL"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          )}

          {/* Add Button */}
          <button
            type="submit"
            disabled={!isValidUrl || isAddingVideo}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 btn-primary px-3 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAddingVideo ? (
              <div className="loading-spinner w-4 h-4"></div>
            ) : (
              'Add'
            )}
          </button>
        </div>
      </form>

      {/* Status Messages */}
      {error && (
        <div className="flex items-center space-x-2 text-red-400 text-sm">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
          </svg>
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="flex items-center space-x-2 text-green-400 text-sm">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
          <span>{success}</span>
        </div>
      )}

      {/* URL Validation Indicator */}
      {url.trim() && (
        <div className="flex items-center space-x-2 text-xs">
          {isValidUrl ? (
            <>
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-green-400">Valid YouTube URL</span>
            </>
          ) : (
            <>
              <div className="w-2 h-2 bg-red-400 rounded-full"></div>
              <span className="text-red-400">Invalid YouTube URL</span>
            </>
          )}
        </div>
      )}
    </div>
  )
}
