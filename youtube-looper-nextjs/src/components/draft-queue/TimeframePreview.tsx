'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { VideoTimeframe } from '@/lib/types/video'

interface TimeframePreviewProps {
  videoId: string
  timeframe: VideoTimeframe
  isVisible: boolean
  onClose: () => void
}

declare global {
  interface Window {
    YT: any
    onYouTubeIframeAPIReady: () => void
  }
}

export function TimeframePreview({ videoId, timeframe, isVisible, onClose }: TimeframePreviewProps) {
  const [playerElement, setPlayerElement] = useState<HTMLDivElement | null>(null)
  const [player, setPlayer] = useState<any>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [hasError, setHasError] = useState(false)
  const monitorRef = useRef<NodeJS.Timeout | null>(null)
  const playerTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Callback ref to ensure we get notified when the element is attached
  const playerRefCallback = (element: HTMLDivElement | null) => {
    console.log('🎬 [PREVIEW] Ref callback triggered:', !!element)
    setPlayerElement(element)
  }

  const stopTimeframeMonitoring = useCallback(() => {
    if (monitorRef.current) {
      clearInterval(monitorRef.current)
      monitorRef.current = null
    }
  }, [])

  // Monitor timeframe boundaries
  const startTimeframeMonitoring = useCallback((playerInstance: any) => {
    if (monitorRef.current) {
      clearInterval(monitorRef.current)
    }

    monitorRef.current = setInterval(() => {
      try {
        if (playerInstance && typeof playerInstance.getCurrentTime === 'function') {
          const currentTime = playerInstance.getCurrentTime()

          if (typeof currentTime === 'number' && !isNaN(currentTime)) {
            // Check if we've reached the end of the timeframe
            if (currentTime >= timeframe.endTime - 0.5) {
              console.log(`🎬 Preview reached timeframe end at ${currentTime}s`)
              stopTimeframeMonitoring()
              // Loop back to start of timeframe
              playerInstance.seekTo(timeframe.startTime, true)
            }
          }
        }
      } catch (error) {
        console.error('❌ Error monitoring preview timeframe:', error)
      }
    }, 500)
  }, [timeframe.endTime, timeframe.startTime, stopTimeframeMonitoring])

  // Initialize YouTube API if not already loaded
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.YT) {
      console.log('🎬 [PREVIEW] Loading YouTube API...')
      const tag = document.createElement('script')
      tag.src = 'https://www.youtube.com/iframe_api'
      const firstScriptTag = document.getElementsByTagName('script')[0]
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag)

      // Set up global callback for when API is ready
      window.onYouTubeIframeAPIReady = () => {
        console.log('🎬 [PREVIEW] YouTube API loaded')
      }
    } else if (window.YT) {
      console.log('🎬 [PREVIEW] YouTube API already available')
    }
  }, [])

  // Create player when visible
  useEffect(() => {
    console.log('🎬 [PREVIEW] useEffect triggered', {
      isVisible,
      hasPlayerElement: !!playerElement,
      hasPlayer: !!player,
      hasYT: !!window.YT,
      hasYTPlayer: !!(window.YT && window.YT.Player)
    })

    if (!isVisible || !playerElement || player) {
      return
    }

    console.log('🎬 [PREVIEW] All conditions met, creating player...')

    if (window.YT && window.YT.Player) {
      console.log('🎬 [PREVIEW] Attempting to create player...', {
        hasYT: !!window.YT,
        hasYTPlayer: !!(window.YT && window.YT.Player),
        hasPlayerElement: !!playerElement,
        videoId,
        timeframe
      })

      if (window.YT && window.YT.Player) {
        setIsLoading(true)
        console.log('🎬 [PREVIEW] Creating YouTube player...')

        // Create a unique ID for this player instance
        const playerId = `preview-player-${Date.now()}`
        playerElement.id = playerId

        try {
          const newPlayer = new window.YT.Player(playerId, {
            height: '180',
            width: '320',
            videoId: videoId,
            playerVars: {
              start: Math.floor(timeframe.startTime),
              autoplay: 0,
              controls: 1,
              modestbranding: 1,
              rel: 0,
              showinfo: 0,
              iv_load_policy: 3, // Hide annotations
              origin: window.location.origin, // Add origin for CORS
            },
        events: {
          onReady: (event: any) => {
            console.log('🎬 [PREVIEW] Player ready!', event.target)
            // Clear the timeout since player is ready
            if (playerTimeoutRef.current) {
              clearTimeout(playerTimeoutRef.current)
              playerTimeoutRef.current = null
            }
            setIsLoading(false)
            setPlayer(event.target)
          },
          onStateChange: (event: any) => {
            const state = event.data
            console.log('🎬 [PREVIEW] State change:', state)
            if (state === 1) { // Playing
              setIsPlaying(true)
              startTimeframeMonitoring(event.target)
            } else if (state === 2) { // Paused
              setIsPlaying(false)
              stopTimeframeMonitoring()
            } else if (state === 0) { // Ended
              setIsPlaying(false)
              stopTimeframeMonitoring()
              // Auto-restart the timeframe
              event.target.seekTo(timeframe.startTime, true)
            }
          },
          onError: (event: any) => {
            console.error('❌ [PREVIEW] Player error:', event.data)
            setIsLoading(false)
            setHasError(true)
          }
        }
      })
          // Set a timeout to detect if player creation hangs
          playerTimeoutRef.current = setTimeout(() => {
            console.error('❌ [PREVIEW] Player creation timeout - onReady never fired')
            setIsLoading(false)
            setHasError(true)
          }, 10000) // 10 second timeout

        } catch (error) {
          console.error('❌ [PREVIEW] Failed to create player:', error)
          setIsLoading(false)
          setHasError(true)
        }
      } else {
        // YouTube API not ready yet, wait a bit and try again
        console.log('🎬 [PREVIEW] YouTube API not ready, waiting...')
        setIsLoading(true)

        let attempts = 0
        const maxAttempts = 10

        const checkAPI = () => {
          attempts++
          console.log(`🎬 [PREVIEW] Checking API attempt ${attempts}/${maxAttempts}`)

          if (window.YT && window.YT.Player) {
            console.log('🎬 [PREVIEW] API now ready, triggering re-render')
            setIsLoading(false)
            // Force re-render by updating a state
            setIsLoading(true)
          } else if (attempts < maxAttempts) {
            setTimeout(checkAPI, 1000)
          } else {
            console.error('🎬 [PREVIEW] Failed to load YouTube API after', maxAttempts, 'attempts')
            setIsLoading(false)
            setHasError(true)
          }
        }

        const timeout = setTimeout(checkAPI, 1000)
        return () => clearTimeout(timeout)
      }
    }
  }, [isVisible, videoId, timeframe, playerElement, player, startTimeframeMonitoring, stopTimeframeMonitoring])



  // Cleanup when component unmounts or becomes invisible
  useEffect(() => {
    return () => {
      stopTimeframeMonitoring()
      if (playerTimeoutRef.current) {
        clearTimeout(playerTimeoutRef.current)
        playerTimeoutRef.current = null
      }
      if (player) {
        try {
          player.destroy()
        } catch (error) {
          console.error('Error destroying preview player:', error)
        }
      }
    }
  }, [player, stopTimeframeMonitoring])

  // Reset player when visibility changes
  useEffect(() => {
    if (!isVisible) {
      stopTimeframeMonitoring()
      if (playerTimeoutRef.current) {
        clearTimeout(playerTimeoutRef.current)
        playerTimeoutRef.current = null
      }
      if (player) {
        try {
          player.destroy()
          setPlayer(null)
        } catch (error) {
          console.error('Error destroying preview player:', error)
        }
      }
    }
  }, [isVisible, player, stopTimeframeMonitoring])

  const handlePlayPause = useCallback(() => {
    if (player) {
      if (isPlaying) {
        player.pauseVideo()
      } else {
        // Ensure we're at the right position before playing
        player.seekTo(timeframe.startTime, true)
        player.playVideo()
      }
    }
  }, [player, isPlaying, timeframe.startTime])

  const handleRestart = () => {
    if (player) {
      player.seekTo(timeframe.startTime, true)
      player.playVideo()
    }
  }

  const handleRetry = () => {
    console.log('🎬 [PREVIEW] Manual retry triggered')
    setHasError(false)
    setIsLoading(true)
    setPlayer(null)
    // Clear any existing timeout
    if (playerTimeoutRef.current) {
      clearTimeout(playerTimeoutRef.current)
      playerTimeoutRef.current = null
    }
    // This will trigger the useEffect to try creating the player again
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isVisible) return

      if (e.key === 'Escape') {
        onClose()
      } else if (e.key === ' ' || e.key === 'Spacebar') {
        e.preventDefault()
        handlePlayPause()
      }
    }

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isVisible, onClose, handlePlayPause])

  if (!isVisible) return null

  console.log('🎬 [PREVIEW] Rendering, playerElement:', !!playerElement)

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={onClose}>
      <div className="bg-dark-800 rounded-lg p-4 max-w-md w-full mx-4" onClick={(e) => e.stopPropagation()}>
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-white font-medium">Timeframe Preview</h3>
          <button
            onClick={onClose}
            className="text-dark-400 hover:text-white p-1"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div className="mb-3">
          <div className="text-sm text-dark-300 mb-2">
            {formatTime(timeframe.startTime)} - {formatTime(timeframe.endTime)} 
            <span className="ml-2 text-primary-400">({timeframe.loopCount} loops)</span>
          </div>
          
          {(() => {
            console.log('🎬 [PREVIEW] Render condition check:', {
              hasError,
              isLoading,
              hasPlayer: !!player,
              condition: hasError ? 'ERROR' : (isLoading || !player) ? 'LOADING' : 'PLAYER_DIV'
            })

            if (hasError) {
              return (
                <div className="bg-dark-700 rounded flex flex-col items-center justify-center h-[180px] w-[320px]">
                  <div className="text-red-400 mb-2">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                  <div className="text-dark-400 text-sm text-center mb-3">
                    Failed to load preview
                  </div>
                  <button
                    onClick={handleRetry}
                    className="btn-primary text-xs px-3 py-1"
                  >
                    Retry
                  </button>
                </div>
              )
            } else if (isLoading) {
              return (
                <div className="bg-dark-700 rounded flex flex-col items-center justify-center h-[180px] w-[320px]">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-2"></div>
                  <div className="text-dark-400 text-sm text-center">
                    {!window.YT ? 'Loading YouTube API...' :
                     !(window.YT && window.YT.Player) ? 'Initializing player...' :
                     'Loading preview...'}
                  </div>
                  <div className="text-dark-500 text-xs mt-1">
                    Video: {videoId}
                  </div>
                </div>
              )
            } else {
              console.log('🎬 [PREVIEW] Rendering player div (player may not exist yet)')
              return (
                <div className="relative">
                  <div ref={playerRefCallback} className="rounded overflow-hidden" />
                  {!player && (
                    <div className="absolute inset-0 bg-dark-700 rounded flex flex-col items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mb-2"></div>
                      <div className="text-dark-400 text-sm text-center">
                        Creating player...
                      </div>
                    </div>
                  )}
                </div>
              )
            }
          })()}
        </div>

        <div className="flex gap-2">
          <button
            onClick={handlePlayPause}
            disabled={isLoading}
            className="btn-primary flex-1 flex items-center justify-center gap-2"
          >
            {isPlaying ? (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                </svg>
                Pause
              </>
            ) : (
              <>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M8 5v14l11-7z"/>
                </svg>
                Play
              </>
            )}
          </button>
          
          <button
            onClick={handleRestart}
            disabled={isLoading}
            className="btn-secondary flex items-center justify-center gap-2"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
            </svg>
            Restart
          </button>
        </div>

        <div className="mt-3 text-xs text-dark-400 text-center space-y-1">
          <div>Preview will loop within the timeframe boundaries</div>
          <div>Press <kbd className="bg-dark-600 px-1 rounded">Space</kbd> to play/pause, <kbd className="bg-dark-600 px-1 rounded">Esc</kbd> to close</div>
        </div>
      </div>
    </div>
  )
}
