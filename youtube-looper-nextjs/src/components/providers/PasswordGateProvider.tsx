'use client'

import { <PERSON>actN<PERSON>, useState, useEffect } from 'react'
import { PasswordGateContext } from '@/hooks/usePasswordGate'

interface PasswordGateProviderProps {
  children: ReactNode
}

const STORAGE_KEY = 'tubli_password_verified'
const SESSION_DURATION = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

export function PasswordGateProvider({ children }: PasswordGateProviderProps) {
  const [isPasswordVerified, setIsPasswordVerified] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Check if password is already verified on mount
  useEffect(() => {
    const checkStoredPassword = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEY)
        if (stored) {
          const { timestamp } = JSON.parse(stored)
          const now = Date.now()
          
          // Check if the stored verification is still valid (within session duration)
          if (now - timestamp < SESSION_DURATION) {
            setIsPasswordVerified(true)
          } else {
            // Clear expired verification
            localStorage.removeItem(STORAGE_KEY)
          }
        }
      } catch (error) {
        console.error('Error checking stored password:', error)
        localStorage.removeItem(STORAGE_KEY)
      } finally {
        setIsLoading(false)
      }
    }

    checkStoredPassword()
  }, [])

  const verifyPassword = async (password: string): Promise<boolean> => {
    try {
      // Call the API route to verify password securely
      const response = await fetch('/api/verify-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      })

      if (!response.ok) {
        console.error('Password verification failed:', response.statusText)
        return false
      }

      const result = await response.json()

      if (result.valid) {
        // Store verification with timestamp
        const verificationData = {
          verified: true,
          timestamp: Date.now()
        }

        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(verificationData))
        } catch (error) {
          console.error('Error storing password verification:', error)
        }

        setIsPasswordVerified(true)
        return true
      }

      return false
    } catch (error) {
      console.error('Error verifying password:', error)
      return false
    }
  }

  const clearPasswordAccess = () => {
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.error('Error clearing password access:', error)
    }
    setIsPasswordVerified(false)
  }

  const value = {
    isPasswordVerified,
    verifyPassword,
    clearPasswordAccess,
  }

  // Show loading state while checking stored password
  if (isLoading) {
    return (
      <div className="min-h-screen bg-dark-900 flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <PasswordGateContext.Provider value={value}>
      {children}
    </PasswordGateContext.Provider>
  )
}
