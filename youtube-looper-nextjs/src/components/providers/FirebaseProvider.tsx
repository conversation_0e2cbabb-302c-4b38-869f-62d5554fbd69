'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { FirebaseApp } from 'firebase/app'
import { Firestore } from 'firebase/firestore'
import { Auth } from 'firebase/auth'
import { initializeFirebase } from '@/lib/firebase/config'

interface FirebaseContextType {
  app: FirebaseApp | null
  db: Firestore | null
  auth: Auth | null
  isInitialized: boolean
  error: string | null
}

const FirebaseContext = createContext<FirebaseContextType>({
  app: null,
  db: null,
  auth: null,
  isInitialized: false,
  error: null,
})

interface FirebaseProviderProps {
  children: ReactNode
}

export function FirebaseProvider({ children }: FirebaseProviderProps) {
  const [firebaseState, setFirebaseState] = useState<FirebaseContextType>({
    app: null,
    db: null,
    auth: null,
    isInitialized: false,
    error: null,
  })

  useEffect(() => {
    const initFirebase = () => {
      try {
        console.log('🔥 Initializing Firebase...')
        const firebase = initializeFirebase()

        if (firebase) {
          setFirebaseState({
            app: firebase.app,
            db: firebase.db,
            auth: firebase.auth,
            isInitialized: true,
            error: null,
          })
          console.log('✅ Firebase initialized successfully')
        } else {
          setFirebaseState(prev => ({
            ...prev,
            isInitialized: true,
            error: 'Firebase configuration is incomplete',
          }))
          console.warn('⚠️ Firebase initialization failed - configuration incomplete')
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown Firebase initialization error'
        setFirebaseState(prev => ({
          ...prev,
          isInitialized: true,
          error: errorMessage,
        }))
        console.error('❌ Firebase initialization error:', error)
      }
    }

    initFirebase()
  }, [])

  return (
    <FirebaseContext.Provider value={firebaseState}>
      {children}
    </FirebaseContext.Provider>
  )
}

export const useFirebase = () => {
  const context = useContext(FirebaseContext)
  if (!context) {
    throw new Error('useFirebase must be used within a FirebaseProvider')
  }
  return context
}
