'use client'

import { ReactNode } from 'react'
import { FirebaseProvider } from './FirebaseProvider'
import { AuthProvider } from './AuthProvider'
import { QueueProvider } from './QueueProvider'
import { DraftQueueProvider } from './DraftQueueProvider'
import { NavigationProvider } from './NavigationProvider'
import { YouTubeProvider } from './YouTubeProvider'
import { ToastProvider } from './ToastProvider'
import { AppWrapper } from './AppWrapper'
import { PasswordGateProvider } from './PasswordGateProvider'
import { PasswordGate } from '@/components/auth/PasswordGate'
import { usePasswordGate } from '@/hooks/usePasswordGate'

interface AppProvidersProps {
  children: ReactNode
}

// Component to conditionally render app content based on password verification
function AppContent({ children }: { children: ReactNode }) {
  const { isPasswordVerified } = usePasswordGate()

  if (!isPasswordVerified) {
    return <PasswordGate />
  }

  return (
    <FirebaseProvider>
      <AuthProvider>
        <AppWrapper>
          <YouTubeProvider>
            <QueueProvider>
              <DraftQueueProvider>
                <NavigationProvider>
                  <ToastProvider>
                    {children}
                  </ToastProvider>
                </NavigationProvider>
              </DraftQueueProvider>
            </QueueProvider>
          </YouTubeProvider>
        </AppWrapper>
      </AuthProvider>
    </FirebaseProvider>
  )
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <PasswordGateProvider>
      <AppContent>
        {children}
      </AppContent>
    </PasswordGateProvider>
  )
}
