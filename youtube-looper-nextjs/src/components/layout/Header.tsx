'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useQueue } from '@/hooks/useQueue'
import { AuthButton } from '@/components/auth/AuthButton'
import { CurrentPlayingInfo } from '@/components/queue/CurrentPlayingInfo'
import { PlayerControls } from '@/components/video-player/PlayerControls'

// Create a global state for header minimize functionality
const headerMinimizeState = {
  isMinimized: false,
  listeners: new Set<(isMinimized: boolean) => void>()
}

export function useHeaderMinimize() {
  const [isMinimized, setIsMinimized] = useState(headerMinimizeState.isMinimized)

  useEffect(() => {
    // Load saved preference from localStorage (only on client side)
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('headerMinimized')
      if (saved === 'true') {
        headerMinimizeState.isMinimized = true
        setIsMinimized(true)
      }
    }

    // Add listener for state changes
    const listener = (newState: boolean) => setIsMinimized(newState)
    headerMinimizeState.listeners.add(listener)

    return () => {
      headerMinimizeState.listeners.delete(listener)
    }
  }, [])

  const toggleMinimized = () => {
    const newState = !headerMinimizeState.isMinimized
    headerMinimizeState.isMinimized = newState

    // Save to localStorage (only on client side)
    if (typeof window !== 'undefined') {
      localStorage.setItem('headerMinimized', newState.toString())
    }

    // Notify all listeners
    headerMinimizeState.listeners.forEach(listener => listener(newState))
  }

  return { isMinimized, toggleMinimized }
}

export function Header() {
  const { user, isAuthenticated, isLoading, isCreatingUser } = useAuth()
  const { currentVideo, isPlaying } = useQueue()
  const { isMinimized, toggleMinimized } = useHeaderMinimize()

  // Determine the status indicator state
  const getStatusIndicator = () => {
    if (isLoading || isCreatingUser) {
      return {
        element: (
          <div className="relative w-3 h-3" title="Creating your session...">
            {/* Outer spinning ring */}
            <div className="absolute inset-0 rounded-full border-2 border-white/20 border-t-primary-500 animate-spin" />
            {/* Inner pulsing dot */}
            <div className="absolute top-1/2 left-1/2 w-1 h-1 -translate-x-1/2 -translate-y-1/2 bg-primary-400 rounded-full animate-pulse" />
          </div>
        )
      }
    }

    const isAnonymous = user?.isAnonymous || false
    const statusText = isAuthenticated && !isAnonymous ? 'Authenticated (Google/Email)' : 'Anonymous session'

    return {
      element: (
        <div
          className={`w-3 h-3 rounded-full transition-colors duration-200 ${
            isAuthenticated && !isAnonymous
              ? 'bg-green-500 shadow-lg shadow-green-500/50'
              : 'bg-yellow-500 shadow-lg shadow-yellow-500/50'
          }`}
          title={statusText}
        />
      )
    }
  }

  return (
    <header className="glassmorphism-strong sticky top-0 z-50 border-b border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Current Playing Info */}
          <div className="flex items-center space-x-6">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center shadow-lg">
                <svg 
                  width="18" 
                  height="18" 
                  viewBox="0 0 24 24" 
                  fill="currentColor"
                  className="text-white"
                >
                  <path d="M8 5v14l11-7z"/>
                </svg>
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-white to-primary-200 bg-clip-text text-transparent">
                Tubli
              </h1>
            </div>

            {/* Current Playing Info */}
            <CurrentPlayingInfo />
          </div>

          {/* Player Controls */}
          <div className="hidden md:flex items-center space-x-4">
            <PlayerControls compact />
          </div>

          {/* Auth Section */}
          <div className="flex items-center space-x-4">
            {/* Connection Status Indicator */}
            {getStatusIndicator().element}

            {/* Auth Button */}
            <AuthButton />

            {/* Minimize Toggle */}
            <button
              onClick={toggleMinimized}
              className="p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 hover:scale-110"
              title={isMinimized ? "Show Media Player" : "Hide Media Player"}
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
                className={`transition-all duration-500 ease-in-out ${
                  isMinimized ? 'rotate-180' : 'rotate-0'
                }`}
              >
                <path d="M7 10l5 5 5-5z"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Player Controls */}
        <div className="md:hidden pb-4">
          <PlayerControls />
        </div>
      </div>
    </header>
  )
}
