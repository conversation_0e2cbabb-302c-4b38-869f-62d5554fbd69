'use client'

import { useState, FormEvent } from 'react'
import { usePasswordGate } from '@/hooks/usePasswordGate'

export function PasswordGate() {
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const { verifyPassword } = usePasswordGate()

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    if (!password.trim()) {
      setError('Please enter a password')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const isValid = await verifyPassword(password)
      if (!isValid) {
        setError('Incorrect password. Please try again.')
        setPassword('')
      }
    } catch (error) {
      setError('An error occurred. Please try again.')
      console.error('Password verification error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-dark-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-2">
            Tubli
          </h1>
          <p className="text-gray-400 mb-8">
            This app is password protected
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="password" className="sr-only">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
              className="w-full px-4 py-3 bg-dark-800 border border-dark-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              disabled={isLoading}
              autoFocus
            />
          </div>

          {error && (
            <div className="text-red-400 text-sm text-center">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full py-3 px-4 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-800 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                Verifying...
              </>
            ) : (
              'Access App'
            )}
          </button>
        </form>

        <div className="text-center text-sm text-gray-500">
          <p>Enter the access password to continue</p>
          {process.env.NODE_ENV === 'development' && (
            <p className="mt-2 text-xs text-gray-600">
              Dev mode: Password is stored securely in Firebase
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
