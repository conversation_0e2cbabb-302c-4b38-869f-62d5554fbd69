/**
 * Integration Tests for Looping Functionality
 * 
 * These tests verify that the rule engine and looping manager work together
 * correctly for all possible looping scenarios.
 */

import { LoopingRuleEngine, TimeframeData, VideoLoopSettings, LoopingState } from '@/lib/looping/LoopingRuleEngine'
import { VideoPlayerLoopingManager } from '@/lib/looping/VideoPlayerLoopingManager'

describe('Looping Integration Tests', () => {
  let ruleEngine: LoopingRuleEngine
  let loopingManager: VideoPlayerLoopingManager
  let mockOnStateUpdate: jest.Mock
  let mockOnSeekTo: jest.Mock
  let mockOnVideoComplete: jest.Mock

  beforeEach(() => {
    ruleEngine = new LoopingRuleEngine()
    mockOnStateUpdate = jest.fn()
    mockOnSeekTo = jest.fn()
    mockOnVideoComplete = jest.fn()
    
    loopingManager = new VideoPlayerLoopingManager(
      mockOnStateUpdate,
      mockOnSeekTo,
      mockOnVideoComplete
    )
  })

  // Helper function to create test timeframes
  const createTimeframes = (count: number, loopCount: number = 1): TimeframeData[] => {
    return Array.from({ length: count }, (_, i) => ({
      id: `tf-${i + 1}`,
      startTime: i * 10,
      endTime: (i * 10) + 5,
      loopCount
    }))
  }

  // Helper function to simulate timeframe completion
  const simulateTimeframeEnd = (videoId: string, timeframes: TimeframeData[], loopSettings: VideoLoopSettings) => {
    loopingManager.handleTimeframeEnd(videoId, timeframes, loopSettings)
  }

  // Helper function to simulate video completion
  const simulateVideoEnd = (videoId: string, timeframes: TimeframeData[], loopSettings: VideoLoopSettings) => {
    return loopingManager.handleVideoEnd(videoId, timeframes, loopSettings)
  }

  describe('TEST 1: Queue Loop Tests', () => {
    test('Queue Loop - Infinite (-1)', () => {
      const videoId = 'video1'
      const timeframes: TimeframeData[] = []
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 1,
        loopMode: 'whole-video-plus-timeframes'
      }
      const queueLoopCount = -1 // Infinite

      // Initialize video
      loopingManager.initializeVideo(videoId, timeframes, loopSettings, queueLoopCount)

      // Simulate video ending (no timeframes, so should complete immediately)
      const result = simulateVideoEnd(videoId, timeframes, loopSettings)

      expect(result).toBe('complete')
      expect(mockOnVideoComplete).toHaveBeenCalled()

      // Verify queue should continue looping (infinite)
      expect(ruleEngine.shouldMoveToNextVideo(-1)).toBe(true)
      expect(ruleEngine.getQueueAction(0, 2, -1)).toBe('next-video')
      expect(ruleEngine.getQueueAction(1, 2, -1)).toBe('loop-queue') // End of queue, should loop
    })

    test('Queue Loop - Finite (3 times)', () => {
      const queueLoopCount = 3

      // Test queue progression
      expect(ruleEngine.getQueueAction(0, 2, queueLoopCount)).toBe('next-video') // Video 0 → 1
      expect(ruleEngine.getQueueAction(1, 2, queueLoopCount)).toBe('loop-queue') // Video 1 → loop back
      
      // After 1 loop (queueLoopCount becomes 2)
      expect(ruleEngine.getQueueAction(1, 2, 2)).toBe('loop-queue')
      
      // After 2 loops (queueLoopCount becomes 1) 
      expect(ruleEngine.getQueueAction(1, 2, 1)).toBe('loop-queue')
      
      // After 3 loops (queueLoopCount becomes 0)
      expect(ruleEngine.getQueueAction(1, 2, 0)).toBe('queue-complete')
    })

    test('Queue Loop - Single run (0 loops)', () => {
      const queueLoopCount = 0

      expect(ruleEngine.getQueueAction(0, 2, queueLoopCount)).toBe('next-video')
      expect(ruleEngine.getQueueAction(1, 2, queueLoopCount)).toBe('queue-complete') // Should stop
    })
  })

  describe('TEST 2: Video Loop Tests', () => {
    test('Video Loop - No Timeframes, Loop 3 times', () => {
      const videoId = 'video1'
      const timeframes: TimeframeData[] = []
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 3,
        loopMode: 'whole-video-plus-timeframes'
      }

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, -1)

      // Get initial state
      let state = loopingManager.getState(videoId)!
      expect(state.videoLoopCount).toBe(0)

      // Simulate video ending 3 times
      for (let i = 0; i < 3; i++) {
        const segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
        
        if (i < 2) {
          // Should loop video
          expect(segment.type).toBe('video-loop')
          expect(segment.seekTo).toBe(0)
          
          // Update state manually (simulating what the manager does)
          state = ruleEngine.updateStateAfterSegment(state, segment)
          expect(state.videoLoopCount).toBe(i + 1)
        } else {
          // Should complete after 3 loops
          expect(segment.type).toBe('video-complete')
        }
      }
    })

    test('Video Loop - With Timeframes, Timeframes-Only Mode', () => {
      const videoId = 'video1'
      const timeframes = createTimeframes(2, 2) // 2 timeframes, each loops 2 times
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 2, // Video loops 2 times
        loopMode: 'timeframes-only'
      }

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, -1)

      // Should start at first timeframe
      const startPos = loopingManager.getVideoStartPosition(videoId, timeframes, loopSettings)
      expect(startPos).toBe(0) // First timeframe start time

      // Should start monitoring immediately in timeframes-only mode
      const shouldMonitor = loopingManager.shouldStartTimeframeMonitoring(videoId, timeframes, loopSettings)
      expect(shouldMonitor).toBe(true)
    })
  })

  describe('TEST 3: Timeframe Loop Tests', () => {
    test('Timeframe Loop - Each timeframe loops individually', () => {
      const videoId = 'video1'
      const timeframes = createTimeframes(2, 3) // 2 timeframes, each loops 3 times
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 1,
        loopMode: 'timeframes-only'
      }

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, -1)
      let state = loopingManager.getState(videoId)!

      // Simulate first timeframe completing 3 times
      for (let loop = 0; loop < 3; loop++) {
        const segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
        
        if (loop < 2) {
          // Should loop current timeframe
          expect(segment.type).toBe('timeframe-loop')
          expect(segment.seekTo).toBe(timeframes[0].startTime)
          
          // Update state
          state = ruleEngine.updateStateAfterSegment(state, segment, timeframes[0].id)
          expect(state.timeframeLoopCounts[timeframes[0].id]).toBe(loop + 1)
        } else {
          // Should move to next timeframe
          expect(segment.type).toBe('next-timeframe')
          expect(segment.seekTo).toBe(timeframes[1].startTime)
          
          state = ruleEngine.updateStateAfterSegment(state, segment)
          expect(state.currentTimeframeIndex).toBe(1)
        }
      }

      // Now simulate second timeframe completing 3 times
      for (let loop = 0; loop < 3; loop++) {
        const segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
        
        if (loop < 2) {
          // Should loop current timeframe
          expect(segment.type).toBe('timeframe-loop')
          expect(segment.seekTo).toBe(timeframes[1].startTime)
          
          state = ruleEngine.updateStateAfterSegment(state, segment, timeframes[1].id)
        } else {
          // All timeframes done, video should complete (videoLoopCount = 1)
          expect(segment.type).toBe('video-complete')
        }
      }
    })

    test('Timeframe Loop - Video loops after all timeframes complete', () => {
      const videoId = 'video1'
      const timeframes = createTimeframes(2, 1) // 2 timeframes, each loops 1 time
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 2, // Video loops 2 times
        loopMode: 'timeframes-only'
      }

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, -1)
      let state = loopingManager.getState(videoId)!

      // Complete first timeframe (1 loop)
      let segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
      expect(segment.type).toBe('next-timeframe')
      state = ruleEngine.updateStateAfterSegment(state, segment)

      // Complete second timeframe (1 loop) - should trigger video loop
      segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
      expect(segment.type).toBe('video-loop')
      expect(segment.seekTo).toBe(timeframes[0].startTime) // Back to first timeframe
      
      state = ruleEngine.updateStateAfterSegment(state, segment)
      expect(state.videoLoopCount).toBe(1)
      expect(state.currentTimeframeIndex).toBe(0)
      
      // All timeframe counters should be reset
      timeframes.forEach(tf => {
        expect(state.timeframeLoopCounts[tf.id]).toBe(0)
      })
    })
  })

  describe('TEST 4: Whole Video + Timeframes Mode', () => {
    test('Plays whole video first, then timeframes', () => {
      const videoId = 'video1'
      const timeframes = createTimeframes(2, 1)
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 1,
        loopMode: 'whole-video-plus-timeframes'
      }

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, -1)

      // Should start at beginning for whole video mode
      const startPos = loopingManager.getVideoStartPosition(videoId, timeframes, loopSettings)
      expect(startPos).toBe(0)

      // Should NOT start monitoring immediately (no timeframes started yet)
      const shouldMonitor = loopingManager.shouldStartTimeframeMonitoring(videoId, timeframes, loopSettings)
      expect(shouldMonitor).toBe(false)

      // Simulate whole video ending
      const result = simulateVideoEnd(videoId, timeframes, loopSettings)
      expect(result).toBe('continue') // Should continue to timeframes

      // Should have seeked to first timeframe
      expect(mockOnSeekTo).toHaveBeenCalledWith(timeframes[0].startTime)
    })

    test('Whole video + timeframes: video loops include timeframes', () => {
      const videoId = 'video1'
      const timeframes = createTimeframes(1, 1)
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 2, // Video loops 2 times (whole video + timeframes each time)
        loopMode: 'whole-video-plus-timeframes'
      }

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, -1)
      let state = loopingManager.getState(videoId)!

      // First video end - should start timeframes
      let segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
      expect(segment.type).toBe('start-timeframes')
      expect(segment.seekTo).toBe(timeframes[0].startTime)

      // Update state to simulate timeframes starting
      state = ruleEngine.updateStateAfterSegment(state, segment)
      // Manually initialize timeframe counters (simulating what executePlaybackSegment does)
      timeframes.forEach(tf => {
        state.timeframeLoopCounts[tf.id] = 0
      })

      // Complete the timeframe
      segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
      // Since timeframe loopCount is 1 and we haven't looped yet, should complete timeframes
      // and check for video loop
      expect(segment.type).toBe('video-loop') // Should loop the entire video (whole + timeframes)
    })
  })

  describe('TEST 5: Complex Integration Scenarios', () => {
    test('Full scenario: Queue with multiple videos, each with different loop settings', () => {
      // Video 1: Timeframes-only, 2 timeframes with 2 loops each, video loops 1 time
      const video1Id = 'video1'
      const video1Timeframes = createTimeframes(2, 2)
      const video1Settings: VideoLoopSettings = {
        videoLoopCount: 1,
        loopMode: 'timeframes-only'
      }

      // Video 2: Whole video + timeframes, 1 timeframe with 1 loop, video loops 2 times  
      const video2Id = 'video2'
      const video2Timeframes = createTimeframes(1, 1)
      const video2Settings: VideoLoopSettings = {
        videoLoopCount: 2,
        loopMode: 'whole-video-plus-timeframes'
      }

      const queueLoopCount = 1 // Queue plays twice total

      // Initialize both videos
      loopingManager.initializeVideo(video1Id, video1Timeframes, video1Settings, queueLoopCount)
      loopingManager.initializeVideo(video2Id, video2Timeframes, video2Settings, queueLoopCount)

      // Test video 1 behavior
      expect(loopingManager.getVideoStartPosition(video1Id, video1Timeframes, video1Settings)).toBe(0)
      expect(loopingManager.shouldStartTimeframeMonitoring(video1Id, video1Timeframes, video1Settings)).toBe(true)

      // Test video 2 behavior  
      expect(loopingManager.getVideoStartPosition(video2Id, video2Timeframes, video2Settings)).toBe(0)
      expect(loopingManager.shouldStartTimeframeMonitoring(video2Id, video2Timeframes, video2Settings)).toBe(false)

      // Verify queue progression
      expect(ruleEngine.getQueueAction(0, 2, queueLoopCount)).toBe('next-video') // Video 1 → Video 2
      expect(ruleEngine.getQueueAction(1, 2, queueLoopCount)).toBe('loop-queue') // Video 2 → loop back
      expect(ruleEngine.getQueueAction(1, 2, 0)).toBe('queue-complete') // After queue loop, complete
    })

    test('Edge case: Video with no timeframes in timeframes-only mode', () => {
      const videoId = 'video1'
      const timeframes: TimeframeData[] = []
      const loopSettings: VideoLoopSettings = {
        videoLoopCount: 2,
        loopMode: 'timeframes-only'
      }

      loopingManager.initializeVideo(videoId, timeframes, loopSettings, -1)

      // Should start at 0 even in timeframes-only mode when no timeframes exist
      const startPos = loopingManager.getVideoStartPosition(videoId, timeframes, loopSettings)
      expect(startPos).toBe(0)

      // Should not start monitoring when no timeframes
      const shouldMonitor = loopingManager.shouldStartTimeframeMonitoring(videoId, timeframes, loopSettings)
      expect(shouldMonitor).toBe(false)

      // Should handle as regular video loop
      const state = loopingManager.getState(videoId)!
      const segment = ruleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
      expect(segment.type).toBe('video-loop')
    })
  })
})
