/**
 * Short Test Videos for Automated Testing
 * 
 * These are real YouTube videos that are very short (under 30 seconds)
 * to make automated testing fast and efficient.
 */

export interface TestVideoData {
  id: string
  title: string
  duration: number // in seconds
  description: string
}

// Collection of short YouTube videos perfect for testing
export const SHORT_TEST_VIDEOS: TestVideoData[] = [
  {
    id: 'dQw4w9WgXcQ', // <PERSON> - but we'll use short segments
    title: '<PERSON> - Never Gonna Give You Up',
    duration: 212, // 3:32 - we'll use short timeframes
    description: 'Classic video, good for testing'
  },
  {
    id: 'jNQXAC9IVRw', // "Me at the zoo" - first YouTube video
    title: 'Me at the zoo',
    duration: 19, // 19 seconds - perfect for testing!
    description: 'First YouTube video ever, very short'
  },
  {
    id: 'hFZFjoX2cGg', // "Sneezing Baby Panda"
    title: 'Sneezing Baby Panda',
    duration: 17, // 17 seconds - great for testing
    description: 'Very short, cute video'
  },
  {
    id: 'kffacxfA7G4', // "Baby Laughing Hysterically"
    title: 'Baby Laughing Hysterically at Ripping Paper',
    duration: 54, // 54 seconds - still short enough
    description: 'Short and sweet'
  },
  {
    id: 'wZZ7oFKsKzY', // "<PERSON><PERSON> [original]"
    title: 'Nyan <PERSON> [original]',
    duration: 184, // 3:04 - we'll use very short timeframes
    description: 'Repetitive, good for loop testing'
  }
]

// Generate short timeframes for testing (5-10 second segments)
export const generateShortTimeframes = (videoId: string, count: number, loopCount: number = 1) => {
  const video = SHORT_TEST_VIDEOS.find(v => v.id === videoId)
  if (!video) throw new Error(`Test video ${videoId} not found`)

  const timeframes = []
  const segmentDuration = Math.min(8, Math.floor(video.duration / count)) // Max 8 seconds per segment
  
  for (let i = 0; i < count; i++) {
    const startTime = i * segmentDuration
    const endTime = Math.min(startTime + segmentDuration - 1, video.duration - 1)
    
    timeframes.push({
      id: `tf-${i + 1}`,
      startTime,
      endTime,
      loopCount,
      title: `Segment ${i + 1}`,
      description: `${segmentDuration}s segment for testing`
    })
  }
  
  return timeframes
}

// Pre-configured test queue configurations using short videos
export const TEST_QUEUE_CONFIGS = {
  // Single video configurations
  SINGLE_SHORT_NO_TIMEFRAMES: {
    name: 'Single Short Video - No Timeframes',
    videos: [{
      id: SHORT_TEST_VIDEOS[1].id, // "Me at the zoo" - 19 seconds
      title: SHORT_TEST_VIDEOS[1].title,
      timeframes: [],
      loopSettings: {
        videoLoopCount: 1,
        loopMode: 'whole-video-plus-timeframes' as const
      }
    }]
  },
  
  SINGLE_SHORT_WITH_TIMEFRAMES: {
    name: 'Single Short Video - With Timeframes',
    videos: [{
      id: SHORT_TEST_VIDEOS[2].id, // "Sneezing Baby Panda" - 17 seconds
      title: SHORT_TEST_VIDEOS[2].title,
      timeframes: generateShortTimeframes(SHORT_TEST_VIDEOS[2].id, 2, 2), // 2 timeframes, 2 loops each
      loopSettings: {
        videoLoopCount: 1,
        loopMode: 'timeframes-only' as const
      }
    }]
  },

  // Multi-video configurations
  MULTI_SHORT_MIXED: {
    name: 'Multiple Short Videos - Mixed Modes',
    videos: [
      {
        id: SHORT_TEST_VIDEOS[1].id, // 19 seconds
        title: SHORT_TEST_VIDEOS[1].title,
        timeframes: generateShortTimeframes(SHORT_TEST_VIDEOS[1].id, 1, 1), // 1 timeframe
        loopSettings: {
          videoLoopCount: 1,
          loopMode: 'timeframes-only' as const
        }
      },
      {
        id: SHORT_TEST_VIDEOS[2].id, // 17 seconds  
        title: SHORT_TEST_VIDEOS[2].title,
        timeframes: [],
        loopSettings: {
          videoLoopCount: 2,
          loopMode: 'whole-video-plus-timeframes' as const
        }
      }
    ]
  },

  // Complex but fast configuration
  COMPLEX_SHORT: {
    name: 'Complex Short Video Test',
    videos: [{
      id: SHORT_TEST_VIDEOS[3].id, // "Baby Laughing" - 54 seconds
      title: SHORT_TEST_VIDEOS[3].title,
      timeframes: generateShortTimeframes(SHORT_TEST_VIDEOS[3].id, 3, 2), // 3 timeframes, 2 loops each
      loopSettings: {
        videoLoopCount: 2,
        loopMode: 'whole-video-plus-timeframes' as const
      }
    }]
  }
}

// Calculate expected test duration for a configuration
export const calculateTestDuration = (config: any, queueLoopCount: number): number => {
  let totalDuration = 0

  const queueRuns = queueLoopCount === -1 ? 1 : Math.max(1, queueLoopCount)

  for (let queueRun = 0; queueRun < queueRuns; queueRun++) {
    for (const video of config.videos) {
      const videoData = SHORT_TEST_VIDEOS.find(v => v.id === video.id)
      if (!videoData) continue

      // Handle both old and new data structures
      const videoLoopCount = video.loopSettings?.videoLoopCount || video.videoLoopCount || 1
      const loopMode = video.loopSettings?.loopMode || video.loopMode || 'whole-video-plus-timeframes'
      const timeframes = video.timeframes || []

      for (let videoLoop = 0; videoLoop < videoLoopCount; videoLoop++) {
        if (loopMode === 'whole-video-plus-timeframes') {
          // Add whole video duration (but cap at 30s for estimation)
          totalDuration += Math.min(videoData.duration, 30)
        }

        // Add timeframe durations
        if (Array.isArray(timeframes)) {
          for (const timeframe of timeframes) {
            const segmentDuration = timeframe.endTime - timeframe.startTime + 1
            totalDuration += segmentDuration * timeframe.loopCount
          }
        } else if (timeframes.count) {
          // Handle timeframes config object
          const segmentDuration = 3 // Assume 3s micro-timeframes
          totalDuration += segmentDuration * timeframes.count * timeframes.loopCount
        }
      }
    }
  }

  return Math.round(totalDuration)
}

// Recommended test configurations with time estimates
export const RECOMMENDED_TEST_CONFIGS = [
  {
    config: TEST_QUEUE_CONFIGS.SINGLE_SHORT_NO_TIMEFRAMES,
    queueLoopCount: 1,
    estimatedDuration: calculateTestDuration(TEST_QUEUE_CONFIGS.SINGLE_SHORT_NO_TIMEFRAMES, 1),
    description: 'Fastest test - single 19s video'
  },
  {
    config: TEST_QUEUE_CONFIGS.SINGLE_SHORT_WITH_TIMEFRAMES,
    queueLoopCount: 1,
    estimatedDuration: calculateTestDuration(TEST_QUEUE_CONFIGS.SINGLE_SHORT_WITH_TIMEFRAMES, 1),
    description: 'Quick timeframe test - ~32s total'
  },
  {
    config: TEST_QUEUE_CONFIGS.MULTI_SHORT_MIXED,
    queueLoopCount: 1,
    estimatedDuration: calculateTestDuration(TEST_QUEUE_CONFIGS.MULTI_SHORT_MIXED, 1),
    description: 'Multi-video test - ~70s total'
  },
  {
    config: TEST_QUEUE_CONFIGS.COMPLEX_SHORT,
    queueLoopCount: 2,
    estimatedDuration: calculateTestDuration(TEST_QUEUE_CONFIGS.COMPLEX_SHORT, 2),
    description: 'Complex test - ~4 minutes total'
  }
]

// Export the shortest video for ultra-fast testing
export const ULTRA_FAST_TEST_VIDEO = SHORT_TEST_VIDEOS[1] // "Me at the zoo" - 19 seconds

// Export a set of micro-timeframes (2-3 seconds each) for the fastest possible testing
export const MICRO_TIMEFRAMES = [
  { id: 'micro-1', startTime: 0, endTime: 2, loopCount: 1, title: 'Micro 1', description: '3s segment' },
  { id: 'micro-2', startTime: 3, endTime: 5, loopCount: 1, title: 'Micro 2', description: '3s segment' },
  { id: 'micro-3', startTime: 6, endTime: 8, loopCount: 1, title: 'Micro 3', description: '3s segment' }
]

console.log('📹 Short Test Videos Configuration Loaded')
console.log(`   Available videos: ${SHORT_TEST_VIDEOS.length}`)
console.log(`   Shortest video: ${ULTRA_FAST_TEST_VIDEO.title} (${ULTRA_FAST_TEST_VIDEO.duration}s)`)
console.log(`   Recommended configs: ${RECOMMENDED_TEST_CONFIGS.length}`)
console.log(`   Fastest test duration: ~${RECOMMENDED_TEST_CONFIGS[0].estimatedDuration}s`)
