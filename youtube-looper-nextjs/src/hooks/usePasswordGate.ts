'use client'

import { createContext, useContext } from 'react'

export interface PasswordGateContextType {
  isPasswordVerified: boolean
  verifyPassword: (password: string) => Promise<boolean>
  clearPasswordAccess: () => void
}

export const PasswordGateContext = createContext<PasswordGateContextType>({
  isPasswordVerified: false,
  verifyPassword: async () => false,
  clearPasswordAccess: () => {},
})

export const usePasswordGate = () => {
  const context = useContext(PasswordGateContext)
  if (!context) {
    throw new Error('usePasswordGate must be used within a PasswordGateProvider')
  }
  return context
}
