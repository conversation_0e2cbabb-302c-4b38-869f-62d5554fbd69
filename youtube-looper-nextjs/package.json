{"name": "youtube-looper-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPatterns=integration", "export": "next build", "firebase:emulators": "firebase emulators:start", "firebase:deploy": "npm run export && firebase deploy", "firebase:deploy:hosting": "npm run export && firebase deploy --only hosting", "firebase:deploy:firestore": "firebase deploy --only firestore", "setup:password": "node scripts/setup-password.js"}, "dependencies": {"@firebase/ai": "^2.0.0", "@hello-pangea/dnd": "^18.0.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "firebase": "^12.0.0", "next": "15.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "15.4.4", "firebase-tools": "^14.11.1", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8", "tailwindcss": "^3.3.0"}}